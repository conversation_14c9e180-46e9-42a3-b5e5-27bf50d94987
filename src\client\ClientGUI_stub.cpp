// ClientGUI stub implementation to disable GUI components
#include <string>

// Forward declare the class to avoid include issues
class ClientGUI {
public:
    static ClientGUI* getInstance() { return nullptr; }
    void updateOperation(const std::string&, bool, const std::string&) {}
    void setBackupState(bool, bool) {}
    void showStatusWindow(bool) {}
};

namespace ClientGUIHelpers {
    bool initializeGUI() { return false; }
    void updatePhase(const std::string&) {}
    void updateConnectionStatus(bool) {}
    void updateOperation(const std::string&, bool, const std::string&) {}
    void showNotification(const std::string&, const std::string&, long) {}
    void updateError(const std::string&) {}
}