#include <iostream>
#include <vector>
#include <string>

int main() {
    std::string keyStr = "\x30\x81\x9f\x30\x0d\x06\x09\x2a\x86\x48\x86\xf7\x0d\x01\x01\x01\x05\x00\x03\x81\x8d\x00\x30\x81\x89\x02\x81\x81\x00\xe7\x0f\xb3\xd4\xf0\x0b\xcf\xe3\xe9\x79\x05\x0d\xa7\xaf\xc8\xd6\x00\x30\xef\x28\xeb\xd5\x78\x32\xd9\xc2\x6e\x53\x14\x6f\xfb\x6b\x76\xa8\xf6\xac\x33\xdf\x55\x77\xc2\xbc\xb7\xd6\x8c\x4e\xcb\x11\x51\x21\x48\xf5\xf1\xfd\xbf\x03\xd9\x01\x07\x6a\xd0\x9f\x35\xe5\x4a\x2f\xf6\xe6\x07\xab\x21\xb5\xfb\xec\xbc\x49\xca\xa0\xd8\x71\x02\x72\xe6\xc7\x55\x2e\x7d\xc5\xf7\xca\x1f\x21\x56\x74\xa8\x25\x58\x86\xfb\x5a\xd1\x19\xfd\xfc\xe9\xb4\x28\x85\x00\x22\xd0\x01\xc2\x0c\xd9\xc7\x17\xa9\x50\x50\x85\xce\xe8\xb8\x22\x15\xba\x65\x02\x03\x01\x00\x01";
    
    std::cout << "RSA key string length: " << keyStr.length() << " bytes" << std::endl;
    
    // Simulate padding username field
    std::string username = "testuser2025";
    std::vector<uint8_t> usernameField(255, 0);
    size_t copySize = std::min(username.size(), size_t(254));
    std::memcpy(usernameField.data(), username.c_str(), copySize);
    
    std::cout << "Username field size: " << usernameField.size() << " bytes" << std::endl;
    std::cout << "Total payload should be: " << usernameField.size() + keyStr.length() << " bytes" << std::endl;
    
    return 0;
}